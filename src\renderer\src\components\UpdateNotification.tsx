import { useState, useEffect } from 'react'

interface UpdateInfo {
  version: string
  releaseDate: string
  releaseName?: string
  releaseNotes?: string
}

interface UpdateNotificationProps {
  onClose: () => void
}

const UpdateNotification: React.FC<UpdateNotificationProps> = ({ onClose }) => {
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null)
  const [isDownloading, setIsDownloading] = useState(false)
  const [downloadProgress, setDownloadProgress] = useState(0)
  const [downloadSpeed, setDownloadSpeed] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [isUpdateReady, setIsUpdateReady] = useState(false)

  useEffect(() => {
    // Listen for update events
    const removeUpdateAvailable = window.api.onUpdateAvailable((info) => {
      setUpdateInfo(info)
    })

    const removeUpdateError = window.api.onUpdateError((errorMsg) => {
      setError(errorMsg)
      setIsDownloading(false)
    })

    const removeDownloadProgress = window.api.onUpdateDownloadProgress((progress) => {
      setDownloadProgress(Math.round(progress.percent))
      const speedMB = (progress.bytesPerSecond / 1024 / 1024).toFixed(1)
      setDownloadSpeed(`${speedMB} MB/s`)
    })

    const removeUpdateDownloaded = window.api.onUpdateDownloaded(() => {
      setIsDownloading(false)
      setIsUpdateReady(true)
    })

    return () => {
      removeUpdateAvailable()
      removeUpdateError()
      removeDownloadProgress()
      removeUpdateDownloaded()
    }
  }, [])

  const handleDownload = async () => {
    setIsDownloading(true)
    setError(null)
    
    try {
      const result = await window.api.downloadUpdate()
      if (!result.success) {
        setError(result.error || 'Failed to download update')
        setIsDownloading(false)
      }
    } catch (err) {
      setError('Failed to start download')
      setIsDownloading(false)
    }
  }

  const handleInstall = async () => {
    try {
      await window.api.installUpdate()
    } catch (err) {
      setError('Failed to install update')
    }
  }

  const handleSkip = () => {
    onClose()
  }

  if (isUpdateReady) {
    return (
      <div className="update-notification update-ready">
        <div className="update-content">
          <h3>Update Ready!</h3>
          <p>The update has been downloaded and is ready to install.</p>
          <p>The application will restart to complete the installation.</p>
          <div className="update-actions">
            <button onClick={handleInstall} className="btn-primary">
              Install & Restart
            </button>
            <button onClick={handleSkip} className="btn-secondary">
              Install Later
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (isDownloading) {
    return (
      <div className="update-notification update-downloading">
        <div className="update-content">
          <h3>Downloading Update...</h3>
          <div className="progress-container">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${downloadProgress}%` }}
              ></div>
            </div>
            <div className="progress-info">
              <span>{downloadProgress}%</span>
              {downloadSpeed && <span>{downloadSpeed}</span>}
            </div>
          </div>
          {error && <p className="error-message">{error}</p>}
        </div>
      </div>
    )
  }

  return (
    <div className="update-notification">
      <div className="update-content">
        <h3>Update Available!</h3>
        {updateInfo && (
          <div className="update-details">
            <p><strong>Version:</strong> {updateInfo.version}</p>
            {updateInfo.releaseName && (
              <p><strong>Release:</strong> {updateInfo.releaseName}</p>
            )}
            {updateInfo.releaseNotes && (
              <div className="release-notes">
                <p><strong>What's New:</strong></p>
                <div dangerouslySetInnerHTML={{ __html: updateInfo.releaseNotes }} />
              </div>
            )}
          </div>
        )}
        {error && <p className="error-message">{error}</p>}
        <div className="update-actions">
          <button onClick={handleDownload} className="btn-primary" disabled={isDownloading}>
            Download Update
          </button>
          <button onClick={handleSkip} className="btn-secondary">
            Skip This Version
          </button>
        </div>
      </div>
    </div>
  )
}

export default UpdateNotification
