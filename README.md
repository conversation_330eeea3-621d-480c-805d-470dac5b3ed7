# pocketbot

An Electron application with React and TypeScript

## Recommended IDE Setup

- [VSCode](https://code.visualstudio.com/) + [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) + [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

## Project Setup

### Install

```bash
$ pnpm install
```

### Environment Configuration

Copy the example environment file and configure your settings:

```bash
$ cp .env.example .env
```

Edit `.env` to configure the bot target URL:

```env
# Bot Configuration
# This URL is used by both the UI and bot automation
VITE_BOT_TARGET_URL=http://localhost:5174
```

The `VITE_BOT_TARGET_URL` environment variable controls:

- The URL that the bot interface navigates to when initialized
- The URL that the bot automation logic uses for its operations
- The URL detection logic for determining if the bot is on the correct site

### Development

```bash
$ pnpm dev
```

### Build

```bash
# For windows
$ pnpm build:win

# For macOS
$ pnpm build:mac

# For Linux
$ pnpm build:linux
```
