/**
 * Environment configuration for the renderer process
 */

// Default values as fallbacks
const DEFAULT_BOT_TARGET_URL = 'http://localhost:5174'

/**
 * Get the bot target URL from environment variables
 * Falls back to default if not set
 */
export const getBotTargetUrl = (): string => {
  return import.meta.env.VITE_BOT_TARGET_URL || DEFAULT_BOT_TARGET_URL
}

/**
 * Configuration object with all environment-based settings
 */
export const config = {
  botTargetUrl: getBotTargetUrl()
} as const
