import { useState, useEffect } from 'react'
import BotInterface from './components/BotInterface'
import UpdateNotification from './components/UpdateNotification'
import UpdateProgress from './components/UpdateProgress'

function App(): React.JSX.Element {
  const [showUpdateNotification, setShowUpdateNotification] = useState(false)
  const [showUpdateProgress, setShowUpdateProgress] = useState(false)

  useEffect(() => {
    // Listen for update events
    const removeUpdateAvailable = window.api.onUpdateAvailable(() => {
      setShowUpdateNotification(true)
      setShowUpdateProgress(false)
    })

    const removeUpdateChecking = window.api.onUpdateChecking(() => {
      setShowUpdateProgress(true)
    })

    const removeUpdateNotAvailable = window.api.onUpdateNotAvailable(() => {
      setShowUpdateProgress(false)
    })

    const removeUpdateError = window.api.onUpdateError(() => {
      setShowUpdateProgress(false)
    })

    const removeUpdateDownloaded = window.api.onUpdateDownloaded(() => {
      setShowUpdateProgress(false)
    })

    return () => {
      removeUpdateAvailable()
      removeUpdateChecking()
      removeUpdateNotAvailable()
      removeUpdateError()
      removeUpdateDownloaded()
    }
  }, [])

  const handleCloseUpdateNotification = (): void => {
    setShowUpdateNotification(false)
  }

  const handleUpdateProgressComplete = (): void => {
    setShowUpdateProgress(false)
  }

  const handleUpdateProgressError = (): void => {
    setShowUpdateProgress(false)
  }

  return (
    <>
      {/* Main Bot Interface */}
      <BotInterface />

      {/* Update Components */}
      {showUpdateNotification && <UpdateNotification onClose={handleCloseUpdateNotification} />}

      {showUpdateProgress && (
        <UpdateProgress
          onComplete={handleUpdateProgressComplete}
          onError={handleUpdateProgressError}
        />
      )}
    </>
  )
}

export default App
