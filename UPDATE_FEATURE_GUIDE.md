# Auto-Update Feature Implementation Guide

## Overview

The PocketBot application now includes a comprehensive auto-update system that automatically checks for updates, downloads them, and allows users to install them with a smooth user experience.

## Features Implemented

### 1. **Automatic Update Checking**
- Checks for updates on app startup (after 3 seconds delay)
- Periodic checks every 4 hours
- Manual check via "Check for Updates" button
- Disabled in development mode for safety

### 2. **User Interface Components**
- **Update Notification**: Beautiful popup showing update details
- **Update Progress**: Real-time download progress with speed indicator
- **Status Messages**: Inline status updates in the main app
- **Responsive Design**: Works on all screen sizes

### 3. **Update Flow**
1. **Check**: App automatically or manually checks for updates
2. **Notify**: User sees notification with update details
3. **Download**: User can choose to download the update
4. **Progress**: Real-time progress indicator during download
5. **Install**: User can install and restart when ready

### 4. **Error Handling**
- Network connectivity issues
- Download failures
- Installation errors
- Graceful fallbacks and user feedback

## Files Modified/Created

### Main Process (`src/main/index.ts`)
- Added electron-updater integration
- Configured auto-updater event handlers
- Added IPC handlers for update operations
- Automatic update checking logic

### Preload Script (`src/preload/index.ts`)
- Exposed update APIs to renderer process
- Event listener management
- Type-safe API definitions

### Type Definitions (`src/preload/index.d.ts`)
- UpdateInfo interface
- UpdateProgress interface
- UpdateAPI interface definitions

### React Components
- **UpdateNotification.tsx**: Main update notification UI
- **UpdateProgress.tsx**: Download progress indicator
- **App.tsx**: Integration of update components

### Styling (`src/renderer/src/assets/main.css`)
- Beautiful gradient backgrounds
- Smooth animations
- Responsive design
- Progress bars and status indicators

### Configuration
- **electron-builder.yml**: Auto-update publish configuration
- **dev-app-update.yml**: Development update settings

## Usage

### For Users
1. **Automatic Updates**: The app will automatically check for updates
2. **Manual Check**: Click "Check for Updates" in the main interface
3. **Download**: When an update is available, click "Download Update"
4. **Install**: After download, click "Install & Restart"

### For Developers

#### Building with Auto-Update
```bash
# Build the application
pnpm build

# Build distributable with auto-update
pnpm build:win  # Windows
pnpm build:mac  # macOS
pnpm build:linux # Linux
```

#### Publishing Updates
1. Update version in `package.json`
2. Build the application
3. Upload the built files to your update server
4. Update the `latest.yml` file on your server

#### Configuration
Update the publish URL in `electron-builder.yml`:
```yaml
publish:
  provider: generic
  url: https://your-update-server.com/updates
  channel: latest
```

## Update Server Setup

You need to host the following files on your update server:

### Required Files
- `latest.yml` - Update metadata
- `pocketbot-{version}-setup.exe` - Windows installer
- `pocketbot-{version}.dmg` - macOS installer
- `pocketbot-{version}.AppImage` - Linux AppImage

### Example `latest.yml`
```yaml
version: 1.0.1
files:
  - url: pocketbot-1.0.1-setup.exe
    sha512: [file-hash]
    size: [file-size]
path: pocketbot-1.0.1-setup.exe
sha512: [file-hash]
releaseDate: '2024-01-15T10:00:00.000Z'
```

## Security Considerations

1. **HTTPS Only**: Always use HTTPS for update servers
2. **Code Signing**: Sign your applications for security
3. **Hash Verification**: electron-updater verifies file integrity
4. **Development Mode**: Auto-updates are disabled in development

## Testing

### Development Testing
1. Set `dev-app-update.yml` to point to a test server
2. Use `electron-builder --publish=never` for local testing
3. Test with different version numbers

### Production Testing
1. Deploy to a staging update server
2. Test the complete update flow
3. Verify rollback scenarios

## Troubleshooting

### Common Issues
1. **Updates not detected**: Check server URL and network connectivity
2. **Download fails**: Verify file permissions and server configuration
3. **Installation fails**: Check code signing and user permissions

### Debug Mode
Enable debug logging by setting environment variable:
```bash
DEBUG=electron-updater
```

## Future Enhancements

Potential improvements for the auto-update system:
1. **Delta Updates**: Only download changed files
2. **Background Downloads**: Download updates in background
3. **Rollback Support**: Ability to rollback failed updates
4. **Update Channels**: Beta, stable, nightly channels
5. **Bandwidth Control**: Limit download speed
6. **Offline Support**: Handle offline scenarios gracefully

## API Reference

### Main Process APIs
- `autoUpdater.checkForUpdates()`: Check for updates
- `autoUpdater.downloadUpdate()`: Download available update
- `autoUpdater.quitAndInstall()`: Install and restart

### Renderer Process APIs
- `window.api.checkForUpdates()`: Manual update check
- `window.api.downloadUpdate()`: Start download
- `window.api.installUpdate()`: Install and restart
- Event listeners for all update states

## Conclusion

The auto-update system provides a seamless experience for keeping the PocketBot application up-to-date. Users receive notifications about new versions, can track download progress, and install updates with minimal disruption to their workflow.
