Stack trace:
Frame         Function      Args
0000005FF160  00021005FE8E (00021026AD60, 00021026AB6E, 000000000000, 0000005FADC0) msys-2.0.dll+0x1FE8E
0000005FF160  0002100467F9 (0000005FD320, 0000005FF160, 000210070B80, 0000006B3E30) msys-2.0.dll+0x67F9
0000005FF160  000210046832 (000000000000, 0000005FBBD8, 000000000000, 000100402000) msys-2.0.dll+0x6832
0000005FF160  000210046DCB (000210312760, 0000005FD320, 000000000000, 000000000000) msys-2.0.dll+0x6DCB
0000005FF160  00021004830F (00007FFE0384, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x830F
0000005FF160  000210070C3C (000000000000, 0000006B0754, 000000000000, 000000000000) msys-2.0.dll+0x30C3C
0000005FF400  7FFBCC8D8B5F (000210040000, 000000000001, 000000000000, 7FFBCC9E8990) ntdll.dll+0x28B5F
0000005FF400  7FFBCC91D4FD (0000005FF300, 000000000000, 0000006B6678, 000000000001) ntdll.dll+0x6D4FD
0000005FF400  7FFBCC91D2AE (0000006B3530, 0000005FF400, 0000006B3E90, 000000000000) ntdll.dll+0x6D2AE
0000005FF400  7FFBCC91D320 (7FFBCC9EB760, 000000000000, 000000384000, 7FFB00000000) ntdll.dll+0x6D320
000000000000  7FFBCC98ECF9 (000000000000, 000000000000, 000000000001, 000000000000) ntdll.dll+0xDECF9
000000000000  7FFBCC97A99A (7FFBCC8B0000, 000000384050, 0000003867EE, 000000000000) ntdll.dll+0xCA99A
000000000000  7FFBCC9243A3 (000000000000, 0000005FFAD0, 000000000000, 000000000000) ntdll.dll+0x743A3
000000000000  7FFBCC9242CE (000000000000, 0000005FFAD0, 000000000000, 000000000000) ntdll.dll+0x742CE
End of stack trace
Loaded modules:
000100400000 zsh.exe
7FFBCC8B0000 ntdll.dll
7FFBCAAD0000 KERNEL32.DLL
7FFBC9CE0000 KERNELBASE.dll
000210040000 msys-2.0.dll
000539B50000 msys-zsh-5.9.dll
0005FCB10000 msys-ncursesw6.dll
7FFBCACD0000 advapi32.dll
7FFBCA9F0000 msvcrt.dll
7FFBCB590000 sechost.dll
7FFBCA0A0000 bcrypt.dll
7FFBCB210000 RPCRT4.dll
7FFBC9360000 CRYPTBASE.DLL
7FFBCA370000 bcryptPrimitives.dll
