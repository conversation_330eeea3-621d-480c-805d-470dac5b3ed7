@import './base.css';

body {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-image: url('./wavy-lines.svg');
  background-size: cover;
  user-select: none;
  margin: 0;
  padding: 0;
}

code {
  font-weight: 600;
  padding: 3px 5px;
  border-radius: 2px;
  background-color: var(--color-background-mute);
  font-family:
    ui-monospace,
    SFMono-Regular,
    SF Mono,
    Menlo,
    Consolas,
    Liberation Mono,
    monospace;
  font-size: 85%;
}

#root {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-bottom: 80px;
  width: 100%;
  height: 100vh;
}

.logo {
  margin-bottom: 20px;
  -webkit-user-drag: none;
  height: 128px;
  width: 128px;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 1.2em #6988e6aa);
}

.creator {
  font-size: 14px;
  line-height: 16px;
  color: var(--ev-c-text-2);
  font-weight: 600;
  margin-bottom: 10px;
}

.text {
  font-size: 28px;
  color: var(--ev-c-text-1);
  font-weight: 700;
  line-height: 32px;
  text-align: center;
  margin: 0 10px;
  padding: 16px 0;
}

.tip {
  font-size: 16px;
  line-height: 24px;
  color: var(--ev-c-text-2);
  font-weight: 600;
}

.react {
  background: -webkit-linear-gradient(315deg, #087ea4 55%, #7c93ee);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.ts {
  background: -webkit-linear-gradient(315deg, #3178c6 45%, #f0dc4e);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.actions {
  display: flex;
  padding-top: 32px;
  margin: -6px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.action {
  flex-shrink: 0;
  padding: 6px;
}

.action a {
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  border: 1px solid transparent;
  text-align: center;
  font-weight: 600;
  white-space: nowrap;
  border-radius: 20px;
  padding: 0 20px;
  line-height: 38px;
  font-size: 14px;
  border-color: var(--ev-button-alt-border);
  color: var(--ev-button-alt-text);
  background-color: var(--ev-button-alt-bg);
}

.action a:hover {
  border-color: var(--ev-button-alt-hover-border);
  color: var(--ev-button-alt-hover-text);
  background-color: var(--ev-button-alt-hover-bg);
}

.versions {
  position: absolute;
  bottom: 30px;
  margin: 0 auto;
  padding: 15px 0;
  font-family: 'Menlo', 'Lucida Console', monospace;
  display: inline-flex;
  overflow: hidden;
  align-items: center;
  border-radius: 22px;
  background-color: #202127;
  backdrop-filter: blur(24px);
}

.versions li {
  display: block;
  float: left;
  border-right: 1px solid var(--ev-c-gray-1);
  padding: 0 20px;
  font-size: 14px;
  line-height: 14px;
  opacity: 0.8;
  &:last-child {
    border: none;
  }
}

@media (max-width: 720px) {
  .text {
    font-size: 20px;
  }
}

@media (max-width: 620px) {
  .versions {
    display: none;
  }
}

@media (max-width: 350px) {
  .tip,
  .actions {
    display: none;
  }
}

/* Update Status Message */
.update-status-message {
  margin: 20px 0;
  padding: 10px 15px;
  background: rgba(108, 117, 125, 0.1);
  border: 1px solid rgba(108, 117, 125, 0.2);
  border-radius: 6px;
  color: var(--color-text);
  font-size: 14px;
  text-align: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Update Notification Styles */
.update-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  color: white;
  animation: slideInRight 0.3s ease-out;
}

.update-notification.update-ready {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.update-notification.update-downloading {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.update-content h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
}

.update-details {
  margin: 15px 0;
  font-size: 14px;
  line-height: 1.5;
}

.update-details p {
  margin: 5px 0;
}

.release-notes {
  margin-top: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  font-size: 13px;
}

.update-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.btn-primary,
.btn-secondary {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
}

.btn-primary:hover {
  background: white;
  transform: translateY(-1px);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.progress-container {
  margin: 15px 0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  opacity: 0.9;
}

.error-message {
  color: #ffcccb;
  font-size: 13px;
  margin: 10px 0;
  padding: 8px;
  background: rgba(255, 0, 0, 0.1);
  border-radius: 4px;
  border-left: 3px solid #ff6b6b;
}

/* Update Progress Component Styles */
.update-progress {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 8px;
  padding: 15px;
  min-width: 300px;
  color: white;
  font-size: 14px;
  z-index: 999;
  animation: slideInUp 0.3s ease-out;
}

.update-progress.error {
  background: rgba(220, 53, 69, 0.9);
}

.update-progress.checking {
  background: rgba(108, 117, 125, 0.9);
}

.update-progress.downloading {
  background: rgba(40, 167, 69, 0.9);
}

.update-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.status-icon {
  font-size: 16px;
}

.status-icon.spinning {
  animation: spin 1s linear infinite;
}

.status-text {
  font-weight: 500;
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  margin-top: 8px;
  opacity: 0.9;
}

.progress-percentage {
  font-weight: 600;
}

.error-details {
  font-size: 12px;
  opacity: 0.9;
  margin-top: 5px;
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Update Tester Styles */
.update-tester {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.9);
  border-radius: 12px;
  padding: 20px;
  max-width: 400px;
  max-height: 500px;
  color: white;
  font-size: 14px;
  z-index: 1001;
  overflow-y: auto;
  border: 2px solid #4facfe;
}

.update-tester h3 {
  margin: 0 0 10px 0;
  color: #4facfe;
  font-size: 16px;
}

.tester-description {
  margin: 0 0 15px 0;
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.4;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.test-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.test-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.test-btn.clear-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.test-btn.clear-btn:hover {
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.test-results h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #4facfe;
}

.results-container {
  max-height: 150px;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  padding: 10px;
  margin-bottom: 15px;
}

.result-item {
  font-size: 11px;
  line-height: 1.4;
  margin-bottom: 8px;
  padding: 6px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  border-left: 3px solid #4facfe;
  font-family: 'Courier New', monospace;
}

.no-results {
  font-size: 12px;
  opacity: 0.6;
  text-align: center;
  margin: 20px 0;
}

.test-info {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 15px;
}

.test-info h4 {
  margin: 0 0 10px 0;
  font-size: 13px;
  color: #4facfe;
}

.test-info ul {
  margin: 0 0 10px 0;
  padding-left: 20px;
  font-size: 11px;
  line-height: 1.4;
}

.test-info li {
  margin-bottom: 4px;
}

.test-info p {
  font-size: 11px;
  opacity: 0.7;
  margin: 0;
  font-style: italic;
}

/* Bot Interface Styles */
.bot-interface {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  overflow: hidden;
  min-height: 300px;
}

.bot-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  text-align: center;
  padding: 40px;
}

.bot-welcome h2 {
  font-size: 3rem;
  margin-bottom: 20px;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bot-welcome p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.btn-begin {
  padding: 15px 40px;
  font-size: 1.2rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-begin:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.bot-interface.expanded {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  transition: all 0.3s ease;
}

.bot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.bot-header h2 {
  margin: 0;
  font-size: 1.2rem;
}

.bot-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-indicator {
  font-size: 12px;
}

.status-text {
  font-size: 14px;
  opacity: 0.9;
}

.bot-content {
  display: flex;
  flex-direction: column;
  padding: 15px;
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 60px);
  min-height: 0;
}

.bot-controls {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.control-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.btn-control {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-control.btn-primary {
  padding: 12px 20px;
  font-size: 15px;
  font-weight: 600;
}

.btn-control:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-start {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.btn-start:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
}

.btn-pause {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
}

.btn-pause:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(250, 112, 154, 0.3);
}

.btn-stop {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.btn-stop:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

/* Development Tools */
.dev-tools {
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 15px;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.dev-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 10px;
}

.dev-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dev-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Position Controls Styles */
.position-controls {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.position-controls h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
}

.position-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(0, 0, 0, 0.2);
  padding: 8px 12px;
  border-radius: 4px;
}

.screen-info {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(0, 0, 0, 0.1);
  padding: 6px 10px;
  border-radius: 4px;
}

.position-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  margin-bottom: 15px;
}

.position-row {
  display: flex;
  gap: 5px;
  align-items: center;
}

.btn-position {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-position:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  background: linear-gradient(135deg, #7c8ef0 0%, #8a5cb8 100%);
}

.btn-position:active {
  transform: translateY(0);
}

.position-fine-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5px;
}

.btn-position-fine {
  padding: 6px 8px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-position-fine:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.btn-position-fine:active {
  transform: translateY(0);
}

/* Responsive Design for Compact Interface */
@media (max-width: 500px) {
  .bot-header {
    flex-direction: column;
    gap: 5px;
    text-align: center;
    padding: 8px 12px;
  }

  .bot-header h2 {
    font-size: 1rem;
  }

  .bot-content {
    padding: 10px;
  }

  .btn-control {
    padding: 6px 12px;
    font-size: 12px;
  }

  .btn-control.btn-primary {
    padding: 10px 16px;
    font-size: 14px;
  }

  .position-controls {
    padding: 10px;
    margin-top: 10px;
  }

  .btn-position {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .btn-position-fine {
    padding: 4px 6px;
    font-size: 9px;
  }
}
