import { useState, useEffect } from 'react'

interface UpdateProgress {
  bytesPerSecond: number
  percent: number
  transferred: number
  total: number
}

interface UpdateProgressProps {
  onComplete?: () => void
  onError?: (error: string) => void
}

const UpdateProgressComponent: React.FC<UpdateProgressProps> = ({ onComplete, onError }) => {
  const [progress, setProgress] = useState<UpdateProgress | null>(null)
  const [isChecking, setIsChecking] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Listen for update events
    const removeUpdateChecking = window.api.onUpdateChecking(() => {
      setIsChecking(true)
      setError(null)
    })

    const removeUpdateError = window.api.onUpdateError((errorMsg) => {
      setError(errorMsg)
      setIsChecking(false)
      onError?.(errorMsg)
    })

    const removeDownloadProgress = window.api.onUpdateDownloadProgress((progressData) => {
      setProgress(progressData)
      setIsChecking(false)
    })

    const removeUpdateDownloaded = window.api.onUpdateDownloaded(() => {
      setProgress(null)
      setIsChecking(false)
      onComplete?.()
    })

    const removeUpdateNotAvailable = window.api.onUpdateNotAvailable(() => {
      setIsChecking(false)
    })

    return () => {
      removeUpdateChecking()
      removeUpdateError()
      removeDownloadProgress()
      removeUpdateDownloaded()
      removeUpdateNotAvailable()
    }
  }, [onComplete, onError])

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatSpeed = (bytesPerSecond: number): string => {
    return formatBytes(bytesPerSecond) + '/s'
  }

  if (error) {
    return (
      <div className="update-progress error">
        <div className="update-status">
          <span className="status-icon">⚠️</span>
          <span className="status-text">Update Error</span>
        </div>
        <div className="error-details">{error}</div>
      </div>
    )
  }

  if (isChecking) {
    return (
      <div className="update-progress checking">
        <div className="update-status">
          <span className="status-icon spinning">🔄</span>
          <span className="status-text">Checking for updates...</span>
        </div>
      </div>
    )
  }

  if (progress) {
    const { percent, transferred, total, bytesPerSecond } = progress
    
    return (
      <div className="update-progress downloading">
        <div className="update-status">
          <span className="status-icon">⬇️</span>
          <span className="status-text">Downloading update...</span>
        </div>
        
        <div className="progress-container">
          <div className="progress-bar">
            <div 
              className="progress-fill" 
              style={{ width: `${Math.round(percent)}%` }}
            ></div>
          </div>
          
          <div className="progress-details">
            <div className="progress-percentage">
              {Math.round(percent)}%
            </div>
            <div className="progress-size">
              {formatBytes(transferred)} / {formatBytes(total)}
            </div>
            <div className="progress-speed">
              {formatSpeed(bytesPerSecond)}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return null
}

export default UpdateProgressComponent
