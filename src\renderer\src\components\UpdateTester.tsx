import { useState } from 'react'

const UpdateTester: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([])

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testCheckForUpdates = async () => {
    addResult('Testing check for updates...')
    try {
      const result = await window.api.checkForUpdates()
      addResult(`Check result: ${JSON.stringify(result, null, 2)}`)
    } catch (error) {
      addResult(`Check error: ${error}`)
    }
  }

  const testDownloadUpdate = async () => {
    addResult('Testing download update...')
    try {
      const result = await window.api.downloadUpdate()
      addResult(`Download result: ${JSON.stringify(result, null, 2)}`)
    } catch (error) {
      addResult(`Download error: ${error}`)
    }
  }

  const testInstallUpdate = async () => {
    addResult('Testing install update...')
    try {
      const result = await window.api.installUpdate()
      addResult(`Install result: ${JSON.stringify(result, null, 2)}`)
    } catch (error) {
      addResult(`Install error: ${error}`)
    }
  }

  const clearResults = () => {
    setTestResults([])
  }

  const simulateUpdateAvailable = () => {
    addResult('Simulating update available event...')
    // This would normally be triggered by the main process
    // but we can test the UI components directly
  }

  return (
    <div className="update-tester">
      <h3>🧪 Update System Tester</h3>
      <p className="tester-description">
        Test the auto-update functionality in development mode. 
        The system cycles through different scenarios every 10 seconds.
      </p>
      
      <div className="test-buttons">
        <button onClick={testCheckForUpdates} className="test-btn">
          🔍 Test Check for Updates
        </button>
        <button onClick={testDownloadUpdate} className="test-btn">
          ⬇️ Test Download Update
        </button>
        <button onClick={testInstallUpdate} className="test-btn">
          🔄 Test Install Update
        </button>
        <button onClick={simulateUpdateAvailable} className="test-btn">
          📢 Simulate Update Available
        </button>
        <button onClick={clearResults} className="test-btn clear-btn">
          🗑️ Clear Results
        </button>
      </div>

      <div className="test-results">
        <h4>Test Results:</h4>
        <div className="results-container">
          {testResults.length === 0 ? (
            <p className="no-results">No test results yet. Click a test button above.</p>
          ) : (
            testResults.map((result, index) => (
              <div key={index} className="result-item">
                {result}
              </div>
            ))
          )}
        </div>
      </div>

      <div className="test-info">
        <h4>📋 Testing Scenarios:</h4>
        <ul>
          <li><strong>Scenario 1:</strong> No updates available</li>
          <li><strong>Scenario 2:</strong> Update available (shows notification)</li>
          <li><strong>Scenario 3:</strong> Network error simulation</li>
        </ul>
        <p><em>The system automatically cycles through these scenarios every 10 seconds when you click "Test Check for Updates".</em></p>
      </div>
    </div>
  )
}

export default UpdateTester
