import { useState, useEffect } from 'react'
import { config } from '../config/env'

interface BotStatus {
  isRunning: boolean
  isPaused: boolean
  currentUrl: string
  status: string
}

const BotInterface: React.FC = () => {
  const [isStarted, setIsStarted] = useState(false)

  const [botStatus, setBotStatus] = useState<BotStatus>({
    isRunning: false,
    isPaused: false,
    currentUrl: '',
    status: 'Ready'
  })

  useEffect(() => {
    // Listen for bot status updates from main process
    const removeBotStatus = window.api.onBotStatusUpdate?.((status: BotStatus) => {
      setBotStatus(status)
    })

    return () => {
      removeBotStatus?.()
    }
  }, [])

  const handleBegin = async (): Promise<void> => {
    try {
      setIsStarted(true)

      // Small delay to allow UI transition to start
      await new Promise((resolve) => setTimeout(resolve, 100))

      // Position bot interface at right side and resize bot browser
      const interfaceWidth = 250
      // Get screen height to make interface fill the full height
      const windowPosition = await window.api.getWindowPosition?.()
      const interfaceHeight = windowPosition?.screenDimensions?.screenHeight || 800

      setBotStatus((prev) => ({ ...prev, status: 'Positioning interface...' }))

      const positionResult = await window.api.positionBotInterface?.(
        interfaceWidth,
        interfaceHeight
      )
      if (positionResult?.success) {
        console.log('Interface positioned successfully:', positionResult)
        setBotStatus((prev) => ({ ...prev, status: 'Interface positioned' }))
      } else {
        console.error('Failed to position interface:', positionResult?.error)
        setBotStatus((prev) => ({ ...prev, status: 'Failed to position interface' }))
      }

      // Another small delay to allow window positioning to complete
      await new Promise((resolve) => setTimeout(resolve, 300))

      setBotStatus((prev) => ({ ...prev, status: 'Initializing browser...' }))

      const result = await window.api.initializeBot?.()
      if (result?.success) {
        setBotStatus((prev) => ({ ...prev, status: 'Browser initialized' }))

        // Navigate to Web
        await window.api.navigateToUrl?.(config.botTargetUrl)
        setBotStatus((prev) => ({
          ...prev,
          currentUrl: config.botTargetUrl,
          status: 'Navigated to Web'
        }))
      }
    } catch (error) {
      console.error('Error initializing bot:', error)
      setBotStatus((prev) => ({ ...prev, status: 'Error initializing bot' }))
    }
  }

  const handleStartBot = async (): Promise<void> => {
    try {
      setBotStatus((prev) => ({ ...prev, isRunning: true, status: 'Bot started' }))
      // Use default configuration for minimal UI
      const defaultConfig = {
        accountType: 'Demo' as const,
        number: '1',
        search: 'picture of a cat'
      }
      await window.api.startBot?.(defaultConfig)
    } catch (error) {
      console.error('Error starting bot:', error)
      setBotStatus((prev) => ({ ...prev, status: 'Error starting bot' }))
    }
  }

  const handleStopBot = async (): Promise<void> => {
    try {
      setBotStatus((prev) => ({
        ...prev,
        isRunning: false,
        isPaused: false,
        status: 'Bot stopped'
      }))
      await window.api.stopBot?.()
    } catch (error) {
      console.error('Error stopping bot:', error)
    }
  }

  const handlePauseBot = async (): Promise<void> => {
    try {
      const newPausedState = !botStatus.isPaused
      setBotStatus((prev) => ({
        ...prev,
        isPaused: newPausedState,
        status: newPausedState ? 'Bot paused' : 'Bot resumed'
      }))
      await window.api.pauseBot?.(newPausedState)
    } catch (error) {
      console.error('Error pausing bot:', error)
    }
  }

  if (!isStarted) {
    return (
      <div className="bot-interface">
        <div className="bot-welcome">
          <h2>🤖 PocketBot</h2>
          <p>Welcome to PocketBot - Your automated browser assistant</p>
          <button onClick={handleBegin} className="btn-begin">
            Begin
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="bot-interface expanded">
      <div className="bot-header">
        <h2>🤖 PocketBot</h2>
        <div className="bot-status">
          <span className={`status-indicator ${botStatus.isRunning ? 'running' : 'stopped'}`}>
            {botStatus.isRunning ? '🟢' : '🔴'}
          </span>
        </div>
      </div>

      <div className="bot-content">
        <div className="bot-controls">
          <div className="control-buttons">
            <button
              onClick={handleStartBot}
              disabled={botStatus.isRunning}
              className="btn-control btn-start btn-primary"
            >
              Start Bot
            </button>
            <button
              onClick={handlePauseBot}
              disabled={!botStatus.isRunning}
              className="btn-control btn-pause"
            >
              {botStatus.isPaused ? 'Resume' : 'Pause'}
            </button>
            <button
              onClick={handleStopBot}
              disabled={!botStatus.isRunning}
              className="btn-control btn-stop"
            >
              Stop
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BotInterface
