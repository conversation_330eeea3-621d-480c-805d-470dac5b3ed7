# 🧪 Auto-Update Testing Guide

## Quick Start - Testing in Development

### 1. **Start the Development Server**
```bash
pnpm dev
```

### 2. **Open the Update Tester**
1. Click the "🧪 Toggle Update Tester" button in the main app
2. A testing panel will appear in the bottom-left corner

### 3. **Test Different Scenarios**

The system automatically cycles through 3 scenarios every 10 seconds:

#### **Scenario 1: No Updates Available**
- Click "🔍 Test Check for Updates"
- You'll see: "No updates available (dev mode)"

#### **Scenario 2: Update Available** 
- Click "🔍 Test Check for Updates" (wait 10 seconds if needed)
- You'll see: "Mock update available"
- A beautiful update notification will appear in the top-right
- Click "Download Update" to test the download flow

#### **Scenario 3: Network Error**
- Click "🔍 Test Check for Updates" (wait 20 seconds if needed)
- You'll see: "Network error (simulated)"

### 4. **Test the Complete Flow**

1. **Check for Updates** → Click "🔍 Test Check for Updates"
2. **Update Available** → Update notification appears
3. **Download** → Click "Download Update" in notification
4. **Progress** → Watch the progress bar with speed indicator
5. **Install Ready** → Click "Install & Restart" when download completes

## Testing Features

### **What You Can Test:**

✅ **Update Detection UI**
- Update notifications with version info
- Release notes display
- Error handling messages

✅ **Download Progress**
- Real-time progress bar
- Download speed indicator
- Progress percentage

✅ **User Interactions**
- "Download Update" button
- "Skip This Version" option
- "Install & Restart" functionality

✅ **Error Scenarios**
- Network connectivity issues
- Download failures
- Installation errors

### **Mock Data Used:**

```javascript
{
  version: '1.1.0',
  releaseDate: '2024-01-15T10:00:00.000Z',
  releaseName: 'Test Update',
  releaseNotes: 'New features and bug fixes'
}
```

## Advanced Testing Methods

### **Method 2: Local Update Server**

1. **Create a local server** with update files
2. **Update `dev-app-update.yml`**:
   ```yaml
   provider: generic
   url: http://localhost:3000/updates
   updaterCacheDirName: pocketbot-updater
   ```
3. **Build and test** with real update files

### **Method 3: Production Testing**

1. **Build the app**: `pnpm build:win`
2. **Deploy to staging server**
3. **Test with real update files**

## Understanding the Test Results

### **Console Output**
Watch the console for detailed logs:
```
Auto-updater disabled in development mode - using mock data
Mock update check result: { updateAvailable: true, updateInfo: {...} }
Simulating update download in development mode
```

### **UI Feedback**
- **Status Messages**: Inline status updates
- **Notifications**: Popup update notifications
- **Progress Bars**: Download progress indicators

## Troubleshooting

### **Common Issues:**

1. **Tester not showing**: Click "🧪 Toggle Update Tester" button
2. **No scenarios cycling**: Wait 10 seconds between tests
3. **Download not starting**: Make sure you're in scenario 2 (update available)

### **Debug Tips:**

1. **Open DevTools**: Press F12 to see console logs
2. **Check Network**: Verify mock data is being sent
3. **Test Timing**: Scenarios change every 10 seconds

## Production Deployment Testing

### **Before Production:**

1. ✅ Test all scenarios in development
2. ✅ Verify UI components work correctly
3. ✅ Test error handling
4. ✅ Check download progress accuracy
5. ✅ Verify install flow (mock)

### **Production Setup:**

1. **Update Server**: Set up HTTPS server with update files
2. **Configuration**: Update `electron-builder.yml` with real URL
3. **Code Signing**: Sign your application for security
4. **Testing**: Test with real version increments

## Next Steps

After testing in development:

1. **Set up update server** (GitHub Releases, S3, etc.)
2. **Configure real URLs** in `electron-builder.yml`
3. **Test with real builds** and version increments
4. **Deploy to production** with confidence!

## Quick Commands

```bash
# Start development with testing
pnpm dev

# Build for testing
pnpm build

# Build distributables
pnpm build:win   # Windows
pnpm build:mac   # macOS  
pnpm build:linux # Linux
```

---

**Happy Testing! 🚀**

The auto-update system is now fully testable in development mode. You can see all the UI components, test user interactions, and verify the complete update flow without needing a real update server.
